<template>
  <div class="doc-import">
    <!-- 上传文档区 -->
    <div v-if="leftArea.markdown == null" class="upload-doc-area">
      <div class="upload-main-container">
        <!-- 知识库选择区 -->
        <div class="kb-selector-container">
          <kb-selector ref="kbSelector0"
            :selectedKbId="selectedKnowledgeBaseId"
            @kb-selected="handleKnowledgeBaseSelected"
            @kb-changed="handleKnowledgeBaseChanged"
          />
        </div>

        <!-- 文档上传区 -->
        <div class="upload-container">
          <div class="upload-file-area" :class="{ 'drag-over': isDragOver }" @drop="handleDrop" @dragover="handleDragOver"
            @dragenter="handleDragEnter" @dragleave="handleDragLeave" @click="triggerFileSelect">
            <div class="upload-content">
              <div class="upload-icon">📁</div>
              <div class="upload-text">
                <p class="main-text">拖拽文件到此处或点击选择文件</p>
                <p class="sub-text">支持多种文档格式</p>
                <p v-if="selectedKnowledgeBase" class="kb-info">
                  将上传到：<span class="kb-name">{{ selectedKnowledgeBase.label }}</span>
                </p>
              </div>
            </div>
            <input ref="fileInput" type="file" :multiple="false" accept=".pdf,.doc,.docx,.txt,.md"
              @change="handleFileSelect" style="display: none;" />
          </div>

          <!-- 上传进度显示 -->
          <div v-if="uploadingFiles.length > 0" class="upload-progress">
            <div v-for="file in uploadingFiles" :key="file.name" class="file-item">
              <span class="file-name">{{ file.name }}</span>
              <div class="progress-bar">
                <div class="progress-fill" :style="{ width: file.progress + '%' }"></div>
              </div>
              <span class="progress-text">{{ file.progress }}%</span>
            </div>
          </div>
        </div>

        <!-- 类型选择器 -->
        <type-selector></type-selector>
      </div>
    </div>
    <!-- 文档审核区 -->
    <div class="doc-audit-area" v-if="ui.showDocAuditArea" style="display: flex;flex-direction: row;">
      <iframe ref="editor0" :src="`${getOpenPluginUrl()}toast-ui-editor/index.html`" @load="handleEditorLoad"
              style="border: 0;width:100%;height:100%;"></iframe>
      <!-- 左区 -->
      <div class="left-area" style="flex: 1; display: flex; flex-direction: column;">
<!--        <textarea class="auto-resize-textarea" v-model="leftArea.markdown"></textarea>-->
      </div>
      <!-- 右区 -->
      <div class="right-area" style="flex: 1; display: flex; flex-direction: column;">
      </div>
    </div>
    <split-doc-view ref="splitDocView0" v-if="ui.showSplitDocView"
                    @on-click-back="handleSplitDocViewClickBack"></split-doc-view>
  </div>
</template>

<script>
import { DocImportMapper } from "../../code/module/doc-import/mapper/DocImportMapper.js";
import appConfig from "../../config/app-config.js";
import KbSelector from "./components/kb-selector.vue";
import LoginService from "../../code/module/platform/service/LoginService.js";
import SplitDocView from "./components/split-doc-view.vue";
import {waitGetObj} from "../../code/util/code-util.js";
import {KbDataSourceMapper} from "../../code/module/cs-mini-kb/mapper/KbDataSourceMapper.js";
import TypeSelector from "./components/type-selector.vue";

export default {
  components: {
    TypeSelector,
    SplitDocView,
    KbSelector
  },
  data() {
    return {
      isDragOver: false,
      uploadingFiles: [],

      // 知识库相关
      selectedKnowledgeBaseId: null,
      selectedKnowledgeBase: null,

      leftArea: {
        title: null,
        markdown: null,
      },

      ui: {
        showDocAuditArea: true,
        showSplitDocView: false,
      }
    }
  },
  methods: {
    getCtx() {
      return this.$ctx
    },
    onShowed() {
      this.$refs.kbSelector0.onShowed();
    },

    /**
     * 设置文档审核区内容
     * @param title
     * @param content
     */
    async setDocAuditAreaContent(title, content) {
      const self = this;
      self.leftArea.title = title;
      self.leftArea.markdown = content;
      await waitGetObj(self.$refs, 'editor0');
      self.$refs.editor0.contentWindow.setMarkdown(this.replaceAttachmentBaseUrl(content), null, {
        scrollTop: 0
      });
    },


    getOpenPluginUrl() {
      return appConfig.openPluginUrl;
    },
    replaceAttachmentBaseUrl(content) {
      return content;
      // return content.replace(/{attachment-base-url}/g, appConfig.attachmentBaseUrl);
    },

    // 知识库选择相关方法
    handleKnowledgeBaseSelected(kb) {
      this.selectedKnowledgeBase = kb;
      console.log('选择了知识库:', kb);
    },

    handleKnowledgeBaseChanged(kbId) {
      this.selectedKnowledgeBaseId = kbId;
      console.log('知识库ID变更为:', kbId);
    },
    onUploadDocDone(pars) {
      const self = this;
      const { title, content } = pars;

      self.setDocAuditAreaContent(title, content);
      console.log(`所有文件上传完毕！${content.length}`)
      console.log(`${title} | ${content.substring(0, 200)}`)
    },
    uploadDoc(files) {
      const self = this;
      const selectedKb = self.$refs.kbSelector0.getSelectedKb();
      if (selectedKb == null) {
        self.$alert(`请选择一个知识库`, { type: 'error' });
      }
      else {
        const selectedKbId = selectedKb.id;

        const loading = this.$loading({ lock: true, text: '上传并处理中，请耐心等待' });
        self.uploadFiles(files, (pars) => {
          loading.close();
          self.onUploadDocDone({
            title: pars.response.data.title,
            content: pars.response.data.markdown,
          })
        }, (error) => {
          loading.close();
          self.$alert(error.message, { type: 'error' });
        })
      }
    },

    handleDragOver(e) {
      e.preventDefault()
      e.stopPropagation()
    },

    handleDragEnter(e) {
      e.preventDefault()
      e.stopPropagation()
      this.isDragOver = true
    },

    handleDragLeave(e) {
      e.preventDefault()
      e.stopPropagation()
      this.isDragOver = false
    },

    handleDrop(e) {
      const self = this;
      e.preventDefault()
      e.stopPropagation()
      this.isDragOver = false

      const files = Array.from(e.dataTransfer.files)
      self.uploadDoc(files);
    },

    triggerFileSelect() {
      this.$refs.fileInput.click()
    },

    handleFileSelect(e) {
      const self = this;

      const files = Array.from(e.target.files)

      self.uploadDoc(files);
      // 清空input值，允许重复选择同一文件
      e.target.value = ''
    },

    uploadFiles(files, onDone, onError) {
      const self = this;

      if (files.length === 0) return

      files.forEach(file => {
        // 检查文件类型
        const allowedTypes = ['.pdf', '.doc', '.docx', '.txt', '.md']
        const fileExtension = '.' + file.name.split('.').pop().toLowerCase()

        if (!allowedTypes.includes(fileExtension)) {
          alert(`不支持的文件类型: ${file.name}`)
          return
        }

        // 添加到上传列表
        const fileItem = {
          name: file.name,
          size: file.size,
          progress: 0,
          file: file
        }

        this.uploadingFiles.push(fileItem)

        // 开始实际上传
        this.uploadFile(fileItem, (pars) => {
          if (self.uploadingFiles.length === 0) {
            if (onDone) {
              onDone(pars);
            }
          }
        }, (error) => {
          onError(error);
        })
      })
    },

    async uploadFile(fileItem, onDone, onError) {
      const self = this;
      const selectedKb = self.$refs.kbSelector0.getSelectedKb();

      await this.getCtx().docImportMapper.uploadDocToMd({
        fileItem, kbId: selectedKb.id,
      }, (pars) => {
        // 上传完成后延迟移除
        setTimeout(() => {
          const index = self.uploadingFiles.indexOf(pars.fileItem)
          if (index > -1) {
            self.uploadingFiles.splice(index, 1)
          }

          if (onDone) {
            onDone({
              ...pars
            });
          }

          // 触发上传成功的回调
          self.$emit('upload-success', {
            file: fileItem.file,
            response: pars.response,
          })
        }, 1000)
      }, (fileItem, message) => {
        onError({
          message
        })
        self.handleUploadError(fileItem, `${message}`)
      })
    },

    handleUploadError(fileItem, errorMessage) {
      fileItem.progress = 0
      fileItem.error = errorMessage

      // 显示错误信息
      // alert(`${errorMessage}`)

      // 从上传列表中移除
      setTimeout(() => {
        const index = this.uploadingFiles.indexOf(fileItem)
        if (index > -1) {
          this.uploadingFiles.splice(index, 1)
        }
      }, 2000)
    },

    handleEditorLoad(e) {
      const self = this;
      // e.target.contentWindow.setMarkdown('');

      self.$refs.editor0.contentWindow.setAttachmentUrlReplacementPlugin(function(url) {
        if (process.env.NODE_ENV === 'production') {
          return url.replace('/attachment-base-url/', appConfig.attachmentBaseUrl.concat('../../'));
        }
        return url.replace('/attachment-base-url/', appConfig.attachmentBaseUrl);
      });

      e.target.contentWindow.addTopRightButtons([
        {
          label: '拆分文档', func: async function () {
            if (self.leftArea.markdown == null || self.leftArea.markdown.length === 0) {
              self.$alert(`文档内容不能为空空`, { type: 'error' });
            }
            else {
              self.ui.showDocAuditArea = false;
              self.ui.showSplitDocView = true;
              const splitDocView = await waitGetObj(self.$refs, 'splitDocView0');
              splitDocView.onShowed({
                title: self.leftArea.title,
                content: self.leftArea.markdown,
                kb: self.selectedKnowledgeBase,
              });
            }
          }
        },
        {
          label: '重新上传', func: function () {
            self.$confirm('确定要重新上传吗？', '确认提示', { type: 'warning' }).then(() => {
              // 点击确认
              self.leftArea.markdown = null;
              self.$refs.editor0.contentWindow.setMarkdown('');
            }).catch(() => {
              // 点击取消
            });
          }
        },
      ]);

    },

    handleSplitDocViewClickBack() {
      const self = this;
      self.ui.showDocAuditArea = true;
      self.ui.showSplitDocView = false;
      self.setDocAuditAreaContent(self.leftArea.title, self.leftArea.markdown)
    },

  },
  async mounted() {
    const self = this;
    const ctx = this.getCtx();

    // 改用客户端登录服务
    if (ctx.cacShellMapper.isInShell()) {
      console.log('在CacShell中运行');
      ctx.loginService = parent.ctx.loginService;
    }
    else {
      console.log('在WebShell中运行');
      ctx.loginService = new LoginService(ctx);
      await ctx.loginService.init();
    }
    ctx.docImportMapper = new DocImportMapper(ctx)
    ctx.kbDataSourceMapper = new KbDataSourceMapper(ctx);

    this.onShowed();
  }
}
</script>

<style scoped lang="less">
.doc-import {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 15px;

  .upload-doc-area {
    .upload-main-container {
      display: flex;
      gap: 15px;
      height: 100%;

      .kb-selector-container {
        flex: 0 0 300px;
        height: 300px;
      }

      .upload-container {
        flex: 1;
        display: flex;
        flex-direction: column;
      }
    }

    .upload-file-area {
      border: 2px dashed #d9d9d9;
      border-radius: 8px;
      padding: 40px 20px;
      text-align: center;
      cursor: pointer;
      transition: all 0.3s ease;
      background-color: #fafafa;
      height: 100%;

      &:hover {
        border-color: #1890ff;
        background-color: #f0f8ff;
      }

      &.drag-over {
        border-color: #1890ff;
        background-color: #e6f7ff;
        transform: scale(1.02);
      }

      .upload-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 16px;

        .upload-icon {
          font-size: 48px;
          opacity: 0.6;
        }

        .upload-text {
          .main-text {
            font-size: 16px;
            color: #333;
            margin: 0 0 8px 0;
            font-weight: 500;
          }

          .sub-text {
            font-size: 14px;
            color: #999;
            margin: 0;
          }

          .kb-info {
            font-size: 12px;
            color: #666;
            margin: 8px 0 0 0;

            .kb-name {
              color: #1890ff;
              font-weight: 600;
            }
          }
        }
      }
    }

    .upload-progress {
      margin-top: 20px;

      .file-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 12px;
        background: #f5f5f5;
        border-radius: 6px;
        margin-bottom: 8px;

        .file-name {
          flex: 0 0 200px;
          font-size: 14px;
          color: #333;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .progress-bar {
          flex: 1;
          height: 6px;
          background: #e0e0e0;
          border-radius: 3px;
          overflow: hidden;

          .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #1890ff, #40a9ff);
            transition: width 0.3s ease;
          }
        }

        .progress-text {
          flex: 0 0 50px;
          font-size: 12px;
          color: #666;
          text-align: right;
        }
      }
    }
  }

  .doc-audit-area {
    height: 100%;

    .left-area {

      height: 100%;
    }

    .right-area {
      height: 100%;
    }

    .auto-resize-textarea {
      flex: 1;
      width: 100%;
      height: 100%;
      border: 1px solid #d9d9d9;
      border-radius: 6px;
      padding: 12px;
      font-size: 14px;
      line-height: 1.5;
      resize: none;
      outline: none;
      font-family: 'Courier New', monospace;
      box-sizing: border-box;

      &:focus {
        border-color: #1890ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
      }
    }
  }
}
</style>