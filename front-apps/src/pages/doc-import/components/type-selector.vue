<template>
  <div class="type-selector">
    <h3 class="selector-title">选择导入类型</h3>
    <div class="options-container">
      <div
        v-for="option in options"
        :key="option.value"
        class="option-card"
        :class="{ 'selected': selectedOption?.value === option.value }"
        @click="handleSelect(option)"
      >
        <div class="option-icon">
          {{ option.icon }}
        </div>
        <div class="option-content">
          <span class="option-title">{{ option.label }}</span>
        </div>
        <div class="option-arrow">
          <i class="arrow-icon">→</i>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
/**
 * 导入类型选择器
 */
export default {
  name: 'type-selector',
  data() {
    return {
      selectedOption: 'parse-doc-import',
      options: [
        {
          label: '解析文档导入',
          value: 'parse-doc-import',
          icon: '📄'
        },
        {
          label: 'JSONL内容导入',
          value: 'jsonl-content-import',
          icon: '📋'
        }
      ]
    }
  },
  methods: {
    handleSelect(option) {
      this.selectedOption = option;
      this.$emit('select', option);
    }
  },
  mounted() {
  }
}
</script>

<style scoped lang="less">
.type-selector {
  max-width: 400px;
  margin: 0 auto;
  padding: 16px;

  .selector-title {
    font-size: 18px;
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 16px;
    text-align: center;
  }

  .options-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
    max-height: 300px;
    overflow-y: auto;
    padding-right: 4px;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f5f9;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #cbd5e1;
      border-radius: 3px;

      &:hover {
        background: #94a3b8;
      }
    }
  }

  .option-card {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    background: #ffffff;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    min-height: 48px;

    &:hover {
      border-color: #3b82f6;
      box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
      transform: translateY(-1px);
    }

    &.selected {
      border-color: #3b82f6;
      background: #eff6ff;
      box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15);

      .option-arrow .arrow-icon {
        color: #3b82f6;
        transform: translateX(2px);
      }
    }

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 3px;
      height: 100%;
      background: #3b82f6;
      border-radius: 0 2px 2px 0;
      transform: scaleY(0);
      transition: transform 0.2s ease;
    }

    &.selected::before {
      transform: scaleY(1);
    }
  }

  .option-icon {
    font-size: 20px;
    margin-right: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background: #f8fafc;
    border-radius: 6px;

    .selected & {
      background: #dbeafe;
    }
  }

  .option-content {
    flex: 1;

    .option-title {
      font-size: 14px;
      font-weight: 500;
      color: #1e293b;
      margin: 0;
    }
  }

  .option-arrow {
    margin-left: 8px;

    .arrow-icon {
      font-size: 14px;
      color: #94a3b8;
      transition: all 0.2s ease;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .type-selector {
    padding: 12px;
    max-width: 100%;

    .selector-title {
      font-size: 16px;
      margin-bottom: 12px;
    }

    .options-container {
      max-height: 250px;
    }

    .option-card {
      padding: 10px 12px;
      min-height: 44px;

      .option-icon {
        font-size: 18px;
        width: 28px;
        height: 28px;
        margin-right: 10px;
      }

      .option-content .option-title {
        font-size: 13px;
      }

      .option-arrow .arrow-icon {
        font-size: 12px;
      }
    }
  }
}
</style>