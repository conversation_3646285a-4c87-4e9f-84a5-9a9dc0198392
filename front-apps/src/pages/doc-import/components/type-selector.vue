<template>
  <div class="type-selector">
    <h3 class="selector-title">选择导入类型</h3>
    <div class="options-container">
      <div
        v-for="option in options"
        :key="option.value"
        class="option-card"
        :class="{ 'selected': selectedOption?.value === option.value }"
        @click="handleSelect(option)"
      >
        <div class="option-icon">
          <i :class="option.icon"></i>
        </div>
        <div class="option-content">
          <h4 class="option-title">{{ option.label }}</h4>
          <p class="option-description">{{ option.description }}</p>
        </div>
        <div class="option-arrow">
          <i class="arrow-icon">→</i>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
/**
 * 导入类型选择器
 */
export default {
  name: 'type-selector',
  data() {
    return {
      selectedOption: null,
      options: [
        {
          label: '解析文档导入',
          value: 'parse-doc-import',
          icon: '📄',
          description: '上传文档文件，系统自动解析内容并导入知识库'
        },
        {
          label: 'JSONL内容导入',
          value: 'jsonl-content-import',
          icon: '📋',
          description: '导入JSONL格式的结构化数据到知识库'
        },
      ]
    }
  },
  methods: {
    handleSelect(option) {
      this.selectedOption = option;
      this.$emit('select', option);
    }
  },
  mounted() {
  }
}
</script>

<style scoped lang="less">
.type-selector {
  max-width: 800px;
  margin: 0 auto;
  padding: 24px;

  .selector-title {
    font-size: 24px;
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 24px;
    text-align: center;
  }

  .options-container {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .option-card {
    display: flex;
    align-items: center;
    padding: 20px 24px;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    background: #ffffff;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;

    &:hover {
      border-color: #3b82f6;
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
      transform: translateY(-2px);
    }

    &.selected {
      border-color: #3b82f6;
      background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);

      .option-arrow .arrow-icon {
        color: #3b82f6;
        transform: translateX(4px);
      }
    }

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 4px;
      height: 100%;
      background: #3b82f6;
      transform: scaleY(0);
      transition: transform 0.3s ease;
    }

    &.selected::before {
      transform: scaleY(1);
    }
  }

  .option-icon {
    font-size: 32px;
    margin-right: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
    border-radius: 12px;

    .selected & {
      background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    }
  }

  .option-content {
    flex: 1;

    .option-title {
      font-size: 18px;
      font-weight: 600;
      color: #1a1a1a;
      margin: 0 0 8px 0;
    }

    .option-description {
      font-size: 14px;
      color: #6b7280;
      margin: 0;
      line-height: 1.5;
    }
  }

  .option-arrow {
    margin-left: 16px;

    .arrow-icon {
      font-size: 20px;
      color: #9ca3af;
      transition: all 0.3s ease;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .type-selector {
    padding: 16px;

    .selector-title {
      font-size: 20px;
      margin-bottom: 20px;
    }

    .option-card {
      padding: 16px 20px;

      .option-icon {
        font-size: 24px;
        width: 48px;
        height: 48px;
        margin-right: 16px;
      }

      .option-content .option-title {
        font-size: 16px;
      }

      .option-content .option-description {
        font-size: 13px;
      }
    }
  }
}
</style>