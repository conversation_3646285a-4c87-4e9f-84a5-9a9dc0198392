<template>
  <div>
    <template v-for="option in options">
      <div @click="handleSelect(option)">
        {{option.label}}
      </div>
    </template>
  </div>
</template>

<script>
/**
 * 导入类型选择器
 */
export default {
  name: 'type-selector',
  data() {
    return {
      options: [
        { label: '解析文档导入', value: 'parse-doc-import' },
        { label: 'JSONL内容导入', value: 'jsonl-content-import' },
      ]
    }
  },
  methods: {
    handleSelect(option) {

    }
  },
  mounted() {
  }
}
</script>

<style scoped lang="less">
</style>